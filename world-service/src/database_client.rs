use std::sync::Arc;
use tonic::transport::{Channel, Endpoint};
use tonic::{Request, Status};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

pub mod client_management {
    tonic::include_proto!("client_management");
}

use client_management::client_management_service_client::ClientManagementServiceClient;
use client_management::*;

#[derive(Clone)]
pub struct DatabaseClient {
    client: ClientManagementServiceClient<Channel>,
}

impl DatabaseClient {
    pub async fn new(database_url: &str) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        info!("Connecting to database service at: {}", database_url);

        let endpoint = Endpoint::from_shared(database_url.to_string())?
            .connect_timeout(std::time::Duration::from_secs(10))
            .timeout(std::time::Duration::from_secs(30))
            .tcp_keepalive(Some(std::time::Duration::from_secs(30)));

        // Retry connection with exponential backoff
        let mut retry_count = 0;
        let max_retries = 3;

        loop {
            match endpoint.connect().await {
                Ok(channel) => {
                    let client = ClientManagementServiceClient::new(channel);
                    info!("Successfully connected to database service");
                    return Ok(Self { client });
                }
                Err(e) => {
                    retry_count += 1;
                    if retry_count >= max_retries {
                        error!("Failed to connect to database service after {} retries: {}", max_retries, e);
                        return Err(format!("Database connection failed after {} retries: {}", max_retries, e).into());
                    }

                    let delay = std::time::Duration::from_secs(retry_count * 2);
                    warn!("Failed to connect to database service (attempt {}/{}): {}. Retrying in {:?}...",
                          retry_count, max_retries, e, delay);
                    tokio::time::sleep(delay).await;
                }
            }
        }
    }

    pub async fn validate_client(&mut self, client_id: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        debug!("Validating client: {}", client_id);
        
        let request = Request::new(ValidateClientRequest {
            client_id: client_id.to_string(),
        });

        match self.client.validate_client(request).await {
            Ok(response) => {
                let resp = response.into_inner();
                debug!("Client validation result for {}: {}", client_id, resp.valid);
                Ok(resp.valid)
            }
            Err(e) => {
                error!("gRPC error validating client: {}", e);
                Err(Box::new(e))
            }
        }
    }

    pub async fn create_session(&mut self, client_id: &str, user_id: &str) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let session_id = Uuid::new_v4().to_string();
        
        debug!("Creating session: {} for client: {}", session_id, client_id);
        
        let request = Request::new(SetSessionRequest {
            session_id: session_id.clone(),
            client_id: client_id.to_string(),
            user_id: user_id.to_string(),
            created_at: chrono::Utc::now().timestamp(),
        });

        match self.client.set_session(request).await {
            Ok(response) => {
                let resp = response.into_inner();
                if resp.success {
                    info!("Successfully created session: {} for client: {}", session_id, client_id);
                    Ok(session_id)
                } else {
                    warn!("Failed to create session: {}", resp.message);
                    Err(resp.message.into())
                }
            }
            Err(e) => {
                error!("gRPC error creating session: {}", e);
                Err(Box::new(e))
            }
        }
    }

    pub async fn get_session(&mut self, session_id: &str) -> Result<Option<(String, String, String)>, Box<dyn std::error::Error + Send + Sync>> {
        debug!("Getting session: {}", session_id);

        let request = Request::new(GetSessionMappingRequest {
            session_id: session_id.to_string(),
        });

        match self.client.get_session_mapping(request).await {
            Ok(response) => {
                let resp = response.into_inner();
                if resp.found {
                    Ok(Some((resp.session_id, resp.client_id, resp.user_id)))
                } else {
                    Ok(None)
                }
            }
            Err(e) => {
                error!("gRPC error getting session: {}", e);
                Err(Box::new(e))
            }
        }
    }
}
