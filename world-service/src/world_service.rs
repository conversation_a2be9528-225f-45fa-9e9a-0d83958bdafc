use futures::{Stream, StreamExt};
use std::collections::HashMap;
use std::pin::Pin;
use std::sync::{Arc, Mutex};
use tonic::{Request, Response, Status, Streaming};
use tracing::{debug, error, info, trace, warn};
use tokio::sync::mpsc;
use tokio_stream::wrappers::ReceiverStream;
use crate::game_logic_client::{GameLogicClientManager, game_logic as client_game_logic, world};
use crate::database_client::DatabaseClient;

pub mod game_logic {
    tonic::include_proto!("game_logic");
}

use world::world_service_server::WorldService;
use world::world_game_logic_service_server::WorldGameLogicService;
use world::*;

pub struct MyWorldService {
    pub client_connections: Arc<Mutex<HashMap<String, ClientConnection>>>,
    pub game_logic_connections: Arc<Mutex<HashMap<u32, GameLogicConnection>>>,
    pub game_logic_manager: Option<Arc<GameLogicClientManager>>,
    pub database_client: Arc<tokio::sync::Mutex<DatabaseClient>>,
}

impl Clone for MyWorldService {
    fn clone(&self) -> Self {
        Self {
            client_connections: self.client_connections.clone(),
            game_logic_connections: self.game_logic_connections.clone(),
            game_logic_manager: self.game_logic_manager.clone(),
            database_client: self.database_client.clone(),
        }
    }
}

pub struct ClientConnection {
    pub session_id: String,
    pub client_id: String,
    pub map_id: i32,
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub sender: mpsc::UnboundedSender<WorldEvent>,
}

pub struct GameLogicConnection {
    pub map_id: u32,
    pub sender: mpsc::UnboundedSender<world::GameLogicEvent>,
}

impl MyWorldService {
    pub async fn new(database_url: &str) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let database_client = DatabaseClient::new(database_url).await?;

        Ok(Self {
            client_connections: Arc::new(Mutex::new(HashMap::new())),
            game_logic_connections: Arc::new(Mutex::new(HashMap::new())),
            game_logic_manager: None,
            database_client: Arc::new(tokio::sync::Mutex::new(database_client)),
        })
    }

    pub async fn new_with_game_logic_manager(game_logic_manager: Arc<GameLogicClientManager>, database_url: &str) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let database_client = DatabaseClient::new(database_url).await?;

        Ok(Self {
            client_connections: Arc::new(Mutex::new(HashMap::new())),
            game_logic_connections: Arc::new(Mutex::new(HashMap::new())),
            game_logic_manager: Some(game_logic_manager),
            database_client: Arc::new(tokio::sync::Mutex::new(database_client)),
        })
    }

    pub fn add_client_connection(&self, session_id: String, client_id: String, map_id: i32, sender: mpsc::UnboundedSender<WorldEvent>) {
        let mut connections = self.client_connections.lock().unwrap();
        connections.insert(session_id.clone(), ClientConnection {
            session_id,
            client_id,
            map_id,
            x: 0.0,
            y: 0.0,
            z: 0.0,
            sender,
        });
    }

    pub fn remove_client_connection(&self, session_id: &str) {
        let mut connections = self.client_connections.lock().unwrap();
        connections.remove(session_id);
    }

    pub fn add_game_logic_connection(&self, map_id: u32, sender: mpsc::UnboundedSender<world::GameLogicEvent>) {
        let mut connections = self.game_logic_connections.lock().unwrap();
        connections.insert(map_id, GameLogicConnection {
            map_id,
            sender,
        });
    }

    pub fn remove_game_logic_connection(&self, map_id: u32) {
        let mut connections = self.game_logic_connections.lock().unwrap();
        connections.remove(&map_id);
    }

    pub fn broadcast_to_clients_in_map(&self, map_id: i32, event: WorldEvent) {
        let connections = self.client_connections.lock().unwrap();
        for connection in connections.values() {
            if connection.map_id == map_id {
                if let Err(e) = connection.sender.send(event.clone()) {
                    warn!("Failed to send event to client {}: {}", connection.client_id, e);
                }
            }
        }
    }

    pub fn send_to_game_logic(&self, map_id: u32, event: world::GameLogicEvent) {
        let connections = self.game_logic_connections.lock().unwrap();
        if let Some(connection) = connections.get(&map_id) {
            if let Err(e) = connection.sender.send(event) {
                warn!("Failed to send event to game logic for map {}: {}", map_id, e);
            }
        }
    }

    pub fn get_nearby_objects_for_client(&self, session_id: &str, x: f32, y: f32, z: f32, map_id: i32, radius: f32) -> Vec<WorldObject> {
        // This is a placeholder implementation
        // In a real implementation, you would query the game logic service or maintain a spatial index
        debug!("Getting nearby objects for client {} at ({}, {}, {}) in map {} with radius {}",
               session_id, x, y, z, map_id, radius);

        // Return empty list for now - this will be populated by game logic service
        vec![]
    }

    /// Send nearby objects to a connecting client
    pub async fn send_nearby_objects_to_client(&self, session_id: &str, client_id: &str, x: f32, y: f32, z: f32, map_id: i32) {
        debug!("Sending nearby objects to client {} at ({}, {}, {}) in map {}", client_id, x, y, z, map_id);

        // Get the client connection to send events
        let client_sender = {
            let connections = self.client_connections.lock().unwrap();
            connections.get(session_id).map(|conn| conn.sender.clone())
        };

        let Some(sender) = client_sender else {
            warn!("No client connection found for session {}", session_id);
            return;
        };

        // Get nearby objects from game logic service if available
        if let Some(game_logic_manager) = &self.game_logic_manager {
            // Get client_id from the client connection
            let client_id = {
                let connections = self.client_connections.lock().unwrap();
                connections.get(session_id).map(|conn| conn.client_id.parse::<u32>().unwrap_or(0)).unwrap_or(0)
            };

            match game_logic_manager.get_nearby_objects(map_id as u32, client_id, x, y, z).await {
                Ok(response) => {
                    debug!("Received {} nearby objects from game logic service", response.objects.len());

                    if !response.objects.is_empty() {
                        // Convert game logic objects to WorldObjects for batch sending
                        let world_objects: Vec<WorldObject> = response.objects
                            .into_iter()
                            .filter_map(|obj| {
                                // Filter out players for now, only include NPCs and Mobs
                                match obj.r#type {
                                    1 => {
                                        // Player - skip for now
                                        debug!("Skipping player object {} for nearby objects", obj.id);
                                        None
                                    }
                                    2 | 3 => {
                                        // NPC or Mob - include in batch
                                        Some(WorldObject {
                                            id: obj.id as u32,
                                            object_type: obj.r#type,
                                            x: obj.x,
                                            y: obj.y,
                                            z: obj.z,
                                            map_id: map_id,
                                            name: format!("Object_{}", obj.id), // Default name
                                            hp: obj.hp, // Default HP
                                            max_hp: obj.max_hp, // Default max HP
                                        })
                                    }
                                    _ => {
                                        debug!("Unknown object type {} for object {}", obj.r#type, obj.id);
                                        None
                                    }
                                }
                            })
                            .collect();

                        if !world_objects.is_empty() {
                            // Send all nearby objects in a single batch update
                            let batch_event = WorldEvent {
                                client_ids: vec![client_id.to_string()],
                                event: Some(world_event::Event::NearbyUpdate(NearbyObjectsUpdate {
                                    objects: world_objects.clone(),
                                })),
                            };

                            if let Err(e) = sender.send(batch_event) {
                                warn!("Failed to send nearby objects batch update to client {}: {}", client_id, e);
                            } else {
                                debug!("Sent {} nearby objects in batch to client {}", world_objects.len(), client_id);
                            }
                        }
                    }
                }
                Err(e) => {
                    warn!("Failed to get nearby objects from game logic service for client {}: {}", client_id, e);
                }
            }
        } else {
            debug!("No game logic manager available, skipping nearby objects");
        }
    }

    /// Convert a game logic object to a WorldEvent based on its type
    fn convert_game_logic_object_to_world_event(&self, obj: &client_game_logic::Object, client_id: &str) -> Option<WorldEvent> {
        // Object types: 1 = Player, 2 = NPC, 3 = Mob (based on common MMORPG conventions)
        match obj.r#type {
            2 => {
                // NPC
                Some(WorldEvent {
                    client_ids: vec![client_id.to_string()],
                    event: Some(world_event::Event::NpcSpawn(NpcSpawnEvent {
                        id: obj.id as u32,
                        pos_x: obj.x,
                        pos_y: obj.y,
                        dest_pos_x: obj.x,
                        dest_pos_y: obj.y,
                        command: 0,
                        target_id: 0,
                        move_mode: 0,
                        hp: 100, // Default HP
                        team_id: 0,
                        status_flag: 0,
                        npc_id: obj.id as u32,
                        quest_id: 0,
                        angle: 0.0,
                        event_status: 0,
                    })),
                })
            }
            3 => {
                // Mob
                Some(WorldEvent {
                    client_ids: vec![client_id.to_string()],
                    event: Some(world_event::Event::MobSpawn(MobSpawnEvent {
                        id: obj.id as u32,
                        pos_x: obj.x,
                        pos_y: obj.y,
                        dest_pos_x: obj.x,
                        dest_pos_y: obj.y,
                        command: 0,
                        target_id: 0,
                        move_mode: 0,
                        hp: 100, // Default HP
                        team_id: 0,
                        status_flag: 0,
                        npc_id: obj.id as u32,
                        quest_id: 0,
                    })),
                })
            }
            1 => {
                // Player - for now we don't send player spawn events to other players on connect
                // This would typically be handled differently (e.g., through a separate player list)
                debug!("Skipping player object {} for nearby objects", obj.id);
                None
            }
            _ => {
                debug!("Unknown object type {} for object {}", obj.r#type, obj.id);
                None
            }
        }
    }
}

#[tonic::async_trait]
impl WorldService for MyWorldService {
    async fn client_handshake(&self, request: Request<HandshakeRequest>) -> Result<Response<HandshakeResponse>, Status> {
        let req = request.into_inner();
        debug!("Client handshake request: {:?}", req);

        // Validate client_id with database service
        match self.database_client.lock().await.validate_client(&req.client_id).await {
            Ok(true) => {
                // Create session
                match self.database_client.lock().await.create_session(&req.client_id, &req.user_id).await {
                    Ok(session_id) => {
                        info!("Successfully created session: {} for client: {}", session_id, req.client_id);
                        Ok(Response::new(HandshakeResponse {
                            success: true,
                            session_id,
                            message: "Handshake successful".to_string(),
                        }))
                    }
                    Err(e) => {
                        error!("Failed to create session for client {}: {}", req.client_id, e);
                        Ok(Response::new(HandshakeResponse {
                            success: false,
                            session_id: String::new(),
                            message: format!("Failed to create session: {}", e),
                        }))
                    }
                }
            }
            Ok(false) => {
                warn!("Invalid client_id in handshake: {}", req.client_id);
                Ok(Response::new(HandshakeResponse {
                    success: false,
                    session_id: String::new(),
                    message: "Invalid client_id".to_string(),
                }))
            }
            Err(e) => {
                error!("Database error during client validation: {}", e);
                Err(Status::internal(format!("Database error: {}", e)))
            }
        }
    }

    async fn join_map(&self, request: Request<JoinMapRequest>) -> Result<Response<JoinMapResponse>, Status> {
        let req = request.into_inner();
        debug!("Join map request: {:?}", req);

        // Validate session and get client_id
        match self.database_client.lock().await.get_session(&req.session_id).await {
            Ok(Some((session_id, client_id, _user_id))) => {
                // Call game logic service to join map
                if let Some(game_logic_manager) = &self.game_logic_manager {
                    match game_logic_manager.join_map(req.map_id, &client_id, req.spawn_x, req.spawn_y, req.spawn_z).await {
                        Ok(entity_id) => {
                            // Store client connection info
                            {
                                let mut connections = self.client_connections.lock().unwrap();
                                connections.insert(session_id.clone(), ClientConnection {
                                    session_id: session_id.clone(),
                                    client_id: client_id.clone(),
                                    map_id: req.map_id as i32,
                                    x: req.spawn_x,
                                    y: req.spawn_y,
                                    z: req.spawn_z,
                                    sender: mpsc::unbounded_channel().0, // Placeholder sender
                                });
                            }

                            info!("Successfully joined map: {} with entity_id: {} for session: {}", req.map_id, entity_id, session_id);
                            Ok(Response::new(JoinMapResponse {
                                success: true,
                                entity_id,
                                message: "Successfully joined map".to_string(),
                            }))
                        }
                        Err(e) => {
                            error!("Failed to join map via game logic service: {}", e);
                            Ok(Response::new(JoinMapResponse {
                                success: false,
                                entity_id: 0,
                                message: format!("Failed to join map: {}", e),
                            }))
                        }
                    }
                } else {
                    error!("No game logic manager available");
                    Err(Status::internal("Game logic manager not available"))
                }
            }
            Ok(None) => {
                warn!("Invalid session_id in join map: {}", req.session_id);
                Ok(Response::new(JoinMapResponse {
                    success: false,
                    entity_id: 0,
                    message: "Invalid session_id".to_string(),
                }))
            }
            Err(e) => {
                error!("Database error during session validation: {}", e);
                Err(Status::internal(format!("Database error: {}", e)))
            }
        }
    }

    async fn get_character(&self, request: Request<CharacterRequest>) -> Result<Response<CharacterResponse>, Status> {
        let req = request.into_inner();
        debug!("GetCharacter request: {:?}", req);
        
        let response = CharacterResponse {
            count: 1,
        };
        Ok(Response::new(response))
    }

    async fn change_map(&self, request: Request<ChangeMapRequest>) -> Result<Response<ChangeMapResponse>, Status> {
        let req = request.into_inner();
        debug!("ChangeMap request: {:?}", req);
        
        let response = ChangeMapResponse {
            id: req.id,
            map_id: 1,
            x: req.x,
            y: req.y,
            move_mode: 0,
            ride_mode: 0,
        };
        Ok(Response::new(response))
    }

    async fn move_character(&self, request: Request<CharacterMoveRequest>) -> Result<Response<CharacterMoveResponse>, Status> {
        let req = request.into_inner();
        debug!("MoveCharacter request: {:?}", req);
        
        // Update client position
        {
            let mut connections = self.client_connections.lock().unwrap();
            if let Some(connection) = connections.get_mut(&req.session_id) {
                connection.x = req.x;
                connection.y = req.y;
                connection.z = req.z;
            }
        }
        
        let response = CharacterMoveResponse {
            id: 1,
            target_id: req.target_id as i32,
            distance: 0,
            x: req.x,
            y: req.y,
            z: req.z,
        };
        Ok(Response::new(response))
    }

    async fn get_target_hp(&self, request: Request<ObjectHpRequest>) -> Result<Response<ObjectHpResponse>, Status> {
        let req = request.into_inner();
        debug!("GetTargetHp request: {:?}", req);
        
        let response = ObjectHpResponse {
            target_id: req.target_id,
            hp: 100, // Placeholder
        };
        Ok(Response::new(response))
    }

    async fn get_nearby_objects(&self, request: Request<NearbyObjectsRequest>) -> Result<Response<NearbyObjectsResponse>, Status> {
        let req = request.into_inner();
        debug!("GetNearbyObjects request: {:?}", req);
        
        let objects = self.get_nearby_objects_for_client(&req.session_id, req.x, req.y, req.z, req.map_id, req.radius);
        
        let response = NearbyObjectsResponse {
            objects,
        };
        Ok(Response::new(response))
    }

    type StreamClientEventsStream = Pin<Box<dyn Stream<Item = Result<WorldEvent, Status>> + Send + Sync + 'static>>;

    async fn stream_client_events(
        &self,
        request: Request<Streaming<ClientEvent>>,
    ) -> Result<Response<Self::StreamClientEventsStream>, Status> {
        trace!("New client stream connection established");

        let mut inbound_stream = request.into_inner();
        let (tx, rx) = mpsc::unbounded_channel();

        let client_connections = self.client_connections.clone();
        let game_logic_connections = self.game_logic_connections.clone();
        let game_logic_manager = self.game_logic_manager.clone();
        let database_client = self.database_client.clone();

        tokio::spawn(async move {
            let mut current_session_id: Option<String> = None;

            while let Some(event) = inbound_stream.next().await {
                match event {
                    Ok(client_event) => {
                        debug!("Received client event: {:?}", client_event);
                        
                        match client_event.event {
                            Some(client_event::Event::Connect(connect_event)) => {
                                debug!("Client {} connected to map {} at position ({}, {}, {})",
                                      client_event.client_id, client_event.map_id,
                                      connect_event.x, connect_event.y, connect_event.z);

                                let session_id = client_event.session_id.clone();
                                let client_id = client_event.client_id.clone();
                                let map_id = client_event.map_id;
                                let x = connect_event.x;
                                let y = connect_event.y;
                                let z = connect_event.z;

                                // Track the session ID for cleanup
                                current_session_id = Some(session_id.clone());

                                // Add client connection to the HashMap with position from connect event
                                {
                                    let mut connections = client_connections.lock().unwrap();
                                    connections.insert(session_id.clone(), ClientConnection {
                                        session_id: session_id.clone(),
                                        client_id: client_id.clone(),
                                        map_id,
                                        x,
                                        y,
                                        z,
                                        sender: tx.clone(),
                                    });
                                }

                                // Send all nearby objects to the client
                                // Create a world service instance from the cloned components
                                let world_service = MyWorldService {
                                    client_connections: client_connections.clone(),
                                    game_logic_connections: game_logic_connections.clone(),
                                    game_logic_manager: game_logic_manager.clone(),
                                    database_client: database_client.clone(),
                                };

                                // Send nearby objects to the connecting client
                                world_service.send_nearby_objects_to_client(&session_id, &client_id, x, y, z, map_id).await;
                            }
                            Some(client_event::Event::Disconnect(_)) => {
                                debug!("Client {} disconnected", client_event.client_id);
                                // Handle client disconnection
                            }
                            Some(client_event::Event::Move(move_event)) => {
                                debug!("Client {} moved to ({}, {}, {})", client_event.client_id, move_event.x, move_event.y, move_event.z);

                                // Update client position
                                {
                                    let mut connections = client_connections.lock().unwrap();
                                    if let Some(connection) = connections.get_mut(&client_event.session_id) {
                                        connection.x = move_event.x;
                                        connection.y = move_event.y;
                                        connection.z = move_event.z;
                                    }
                                }

                                // Send PlayerMoveEvent to game logic service
                                let player_move_event = world::GameLogicEvent {
                                    client_ids: vec![],
                                    map_id: client_event.map_id,
                                    event: Some(world::game_logic_event::Event::PlayerMove(world::PlayerMoveEvent {
                                        session_id: client_event.session_id.clone(),
                                        client_id: client_event.client_id.clone(),
                                        x: move_event.x,
                                        y: move_event.y,
                                        z: move_event.z,
                                    })),
                                };

                                // Send to game logic service for the appropriate map
                                let game_logic_connections = game_logic_connections.lock().unwrap();
                                if let Some(connection) = game_logic_connections.get(&(client_event.map_id as u32)) {
                                    debug!("Sending player move event to game logic for map {}", client_event.map_id);
                                    if let Err(e) = connection.sender.send(player_move_event) {
                                        warn!("Failed to send player move event to game logic for map {}: {}", client_event.map_id, e);
                                    }
                                    debug!("Sent player move event to game logic for map {}", client_event.map_id);
                                }
                            }
                            Some(client_event::Event::MapChange(map_change_event)) => {
                                debug!("Client {} changed from map {} to map {}", client_event.client_id, map_change_event.old_map_id, map_change_event.new_map_id);
                                // Handle map change
                            }
                            None => {
                                warn!("Received client event with no event data");
                            }
                        }
                    }
                    Err(e) => {
                        error!("Error receiving client event: {:?}", e);
                        break;
                    }
                }
            }

            // Clean up client connection when stream ends
            if let Some(session_id) = current_session_id {
                let mut connections = client_connections.lock().unwrap();
                connections.remove(&session_id);
                debug!("Client connection {} removed from connections", session_id);
            }

            trace!("Client event stream ended");
        });

        let outbound_stream = tokio_stream::wrappers::UnboundedReceiverStream::new(rx).map(|msg| Ok(msg));
        Ok(Response::new(Box::pin(outbound_stream) as Self::StreamClientEventsStream))
    }
}

pub struct MyWorldGameLogicService {
    pub world_service: Arc<MyWorldService>,
}

impl MyWorldGameLogicService {
    pub fn new(world_service: Arc<MyWorldService>) -> Self {
        Self {
            world_service,
        }
    }
}

#[tonic::async_trait]
impl WorldGameLogicService for MyWorldGameLogicService {
    type StreamGameEventsStream = Pin<Box<dyn Stream<Item = Result<world::GameLogicEvent, Status>> + Send + Sync + 'static>>;

    async fn stream_game_events(
        &self,
        request: Request<Streaming<world::GameLogicEvent>>,
    ) -> Result<Response<Self::StreamGameEventsStream>, Status> {
        trace!("New game logic stream connection established");

        let mut inbound_stream = request.into_inner();
        let (tx, rx) = mpsc::unbounded_channel();

        let world_service = self.world_service.clone();

        tokio::spawn(async move {
            while let Some(event) = inbound_stream.next().await {
                match event {
                    Ok(game_event) => {
                        debug!("Received game event: {:?}", game_event);
                        
                        // Convert game logic events to world events and broadcast to relevant clients
                        match game_event.event {
                            Some(world::game_logic_event::Event::NpcSpawn(npc_spawn)) => {
                                let _world_event = WorldEvent {
                                    client_ids: game_event.client_ids.clone(),
                                    event: Some(world_event::Event::NpcSpawn(npc_spawn)),
                                };
                                // Broadcast to clients - for now broadcast to all clients in the map
                                // In a real implementation, you'd determine the map from the event
                                // world_service.broadcast_to_clients_in_map(game_event.map_id, world_event);
                            }
                            Some(world::game_logic_event::Event::MobSpawn(mob_spawn)) => {
                                let _world_event = WorldEvent {
                                    client_ids: game_event.client_ids.clone(),
                                    event: Some(world_event::Event::MobSpawn(mob_spawn)),
                                };
                                // Broadcast to clients
                            }
                            Some(world::game_logic_event::Event::ObjectDespawn(despawn)) => {
                                let _world_event = WorldEvent {
                                    client_ids: game_event.client_ids.clone(),
                                    event: Some(world_event::Event::ObjectDespawn(despawn)),
                                };
                                // Broadcast to clients
                            }
                            _ => {
                                debug!("Unhandled game logic event type");
                            }
                        }
                        
                        // Echo the event back for now
                        if let Err(e) = tx.send(game_event) {
                            error!("Failed to send game event: {:?}", e);
                        }
                    }
                    Err(e) => {
                        error!("Error receiving game event: {:?}", e);
                        break;
                    }
                }
            }
            trace!("Game logic event stream ended");
        });

        let outbound_stream = tokio_stream::wrappers::UnboundedReceiverStream::new(rx).map(|msg| Ok(msg));
        Ok(Response::new(Box::pin(outbound_stream) as Self::StreamGameEventsStream))
    }
}
