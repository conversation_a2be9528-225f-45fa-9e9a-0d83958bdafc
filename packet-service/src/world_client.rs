use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use tonic::transport::Channel;
use tracing::{debug, error, info, warn};
use futures::{Stream, StreamExt};

pub mod world {
    tonic::include_proto!("world");
}

pub mod character_common {
    tonic::include_proto!("character_common");
}

pub mod game {
    use super::character_common;
    tonic::include_proto!("game");
}

use world::world_service_client::WorldServiceClient;

#[derive(Clone, Debug)]
pub struct WorldClient {
    client: WorldServiceClient<Channel>,
    event_sender: Option<mpsc::UnboundedSender<world::ClientEvent>>,
}

impl WorldClient {
    pub async fn connect(endpoint: &str) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let client = WorldServiceClient::connect(endpoint.to_string()).await?;
        Ok(WorldClient { 
            client,
            event_sender: None,
        })
    }

    pub async fn get_character(
        &mut self,
        token: &str,
        user_id: &str,
        char_id: &str,
        session_id: &str,
    ) -> Result<world::CharacterResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = world::CharacterRequest {
            token: token.to_string(),
            user_id: user_id.to_string(),
            char_id: char_id.to_string(),
            session_id: session_id.to_string(),
        };

        let response = self.client.get_character(request).await?;
        Ok(response.into_inner())
    }

    pub async fn change_map(
        &mut self,
        id: i32,
        x: f32,
        y: f32,
    ) -> Result<world::ChangeMapResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = world::ChangeMapRequest {
            id,
            x,
            y,
        };

        let response = self.client.change_map(request).await?;
        Ok(response.into_inner())
    }

    pub async fn move_character(
        &mut self,
        session_id: &str,
        target_id: u32,
        x: f32,
        y: f32,
        z: f32,
    ) -> Result<world::CharacterMoveResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = world::CharacterMoveRequest {
            session_id: session_id.to_string(),
            target_id,
            x,
            y,
            z,
        };

        let response = self.client.move_character(request).await?;
        Ok(response.into_inner())
    }

    pub async fn get_target_hp(
        &mut self,
        session_id: &str,
        target_id: u32,
    ) -> Result<world::ObjectHpResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = world::ObjectHpRequest {
            session_id: session_id.to_string(),
            target_id,
        };

        let response = self.client.get_target_hp(request).await?;
        Ok(response.into_inner())
    }

    pub async fn get_nearby_objects(
        &mut self,
        session_id: &str,
        x: f32,
        y: f32,
        z: f32,
        map_id: i32,
        radius: f32,
    ) -> Result<world::NearbyObjectsResponse, Box<dyn std::error::Error + Send + Sync>> {
        let request = world::NearbyObjectsRequest {
            session_id: session_id.to_string(),
            x,
            y,
            z,
            map_id,
            radius,
        };

        let response = self.client.get_nearby_objects(request).await?;
        Ok(response.into_inner())
    }

    pub async fn start_client_event_stream(
        &mut self,
        outbound_receiver: mpsc::UnboundedReceiver<world::ClientEvent>,
    ) -> Result<mpsc::UnboundedReceiver<world::WorldEvent>, Box<dyn std::error::Error + Send + Sync>> {
        let (inbound_sender, inbound_receiver) = mpsc::unbounded_channel();
        
        // Create the bidirectional stream
        let outbound_stream = tokio_stream::wrappers::UnboundedReceiverStream::new(outbound_receiver);
        
        let response = self.client.stream_client_events(outbound_stream).await?;
        let mut inbound_stream = response.into_inner();

        // Spawn task to handle incoming events from world service
        tokio::spawn(async move {
            while let Some(event) = inbound_stream.next().await {
                match event {
                    Ok(world_event) => {
                        debug!("Received world event: {:?}", world_event);
                        if let Err(e) = inbound_sender.send(world_event) {
                            error!("Failed to forward world event: {}", e);
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Error receiving world event: {}", e);
                        break;
                    }
                }
            }
            info!("World event stream ended");
        });

        Ok(inbound_receiver)
    }

    pub fn send_client_event(&self, event: world::ClientEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(sender) = &self.event_sender {
            sender.send(event)?;
            Ok(())
        } else {
            Err("Event sender not initialized".into())
        }
    }
}

#[derive(Clone, Debug)]
pub struct WorldClientManager {
    clients: Arc<Mutex<HashMap<String, WorldClient>>>,
    event_senders: Arc<Mutex<HashMap<String, mpsc::UnboundedSender<world::ClientEvent>>>>,
    event_receivers: Arc<Mutex<HashMap<String, mpsc::UnboundedReceiver<world::WorldEvent>>>>,
}

impl WorldClientManager {
    pub fn new() -> Self {
        Self {
            clients: Arc::new(Mutex::new(HashMap::new())),
            event_senders: Arc::new(Mutex::new(HashMap::new())),
            event_receivers: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn add_client_connection(
        &self,
        session_id: String,
        client_id: String,
        position: &crate::character_common::Location,
        world_endpoint: String,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut clients = self.clients.lock().await;
        
        if clients.contains_key(&session_id) {
            warn!("World client for session {} already exists", session_id);
            return Ok(());
        }

        let mut client = WorldClient::connect(&world_endpoint).await?;
        
        // Set up bidirectional communication
        let (outbound_sender, outbound_receiver) = mpsc::unbounded_channel();
        let inbound_receiver = client.start_client_event_stream(outbound_receiver).await?;

        // Send initial connect event
        let connect_event = world::ClientEvent {
            session_id: session_id.clone(),
            client_id: client_id.clone(),
            map_id: position.map_id as i32,
            event: Some(world::client_event::Event::Connect(world::ClientConnectEvent {
                x: position.x as f32,
                y: position.y as f32,
                z: 100.0,
            })),
        };

        outbound_sender.send(connect_event)?;

        clients.insert(session_id.clone(), client);
        
        let mut senders = self.event_senders.lock().await;
        senders.insert(session_id.clone(), outbound_sender);
        
        let mut receivers = self.event_receivers.lock().await;
        receivers.insert(session_id.clone(), inbound_receiver);

        info!("Added world client connection for session {} on map {}", session_id, position.map_id);
        Ok(())
    }

    pub async fn remove_client_connection(&self, session_id: &str) {
        // Send disconnect event before removing
        if let Err(e) = self.send_client_disconnect_event(session_id).await {
            warn!("Failed to send disconnect event for session {}: {}", session_id, e);
        }

        let mut clients = self.clients.lock().await;
        clients.remove(session_id);
        
        let mut senders = self.event_senders.lock().await;
        senders.remove(session_id);
        
        let mut receivers = self.event_receivers.lock().await;
        receivers.remove(session_id);

        info!("Removed world client connection for session {}", session_id);
    }

    pub async fn send_client_move_event(
        &self,
        session_id: &str,
        client_id: &str,
        x: f32,
        y: f32,
        z: f32,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let senders = self.event_senders.lock().await;
        if let Some(sender) = senders.get(session_id) {
            let move_event = world::ClientEvent {
                session_id: session_id.to_string(),
                client_id: client_id.to_string(),
                map_id: 0, // Will be updated by world service based on stored connection info
                event: Some(world::client_event::Event::Move(world::ClientMoveEvent {
                    x,
                    y,
                    z,
                })),
            };
            sender.send(move_event)?;
        }
        Ok(())
    }

    pub async fn send_client_map_change_event(&self, session_id: &str, new_map_id: u32) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let senders = self.event_senders.lock().await;
        if let Some(sender) = senders.get(session_id) {
            let map_change_event = world::ClientEvent {
                session_id: session_id.to_string(),
                client_id: session_id.to_string(),
                map_id: new_map_id as i32,
                event: Some(world::client_event::Event::MapChange(world::ClientMapChangeEvent {
                    old_map_id: 0, // We don't track the old map ID in this context
                    new_map_id: new_map_id as i32,
                    x: 0.0, // Default position - could be enhanced to track actual position
                    y: 0.0,
                    z: 0.0,
                })),
            };
            sender.send(map_change_event)?;
        }
        Ok(())
    }

    pub async fn send_client_disconnect_event(&self, session_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let senders = self.event_senders.lock().await;
        if let Some(sender) = senders.get(session_id) {
            let disconnect_event = world::ClientEvent {
                session_id: session_id.to_string(),
                client_id: session_id.to_string(),
                map_id: 0,
                event: Some(world::client_event::Event::Disconnect(world::ClientDisconnectEvent {})),
            };
            sender.send(disconnect_event)?;
        }
        Ok(())
    }

    pub async fn get_nearby_objects(
        &self,
        session_id: &str,
        x: f32,
        y: f32,
        z: f32,
        map_id: i32,
        radius: f32,
    ) -> Result<world::NearbyObjectsResponse, Box<dyn std::error::Error + Send + Sync>> {
        let mut clients = self.clients.lock().await;
        if let Some(client) = clients.get_mut(session_id) {
            return client.get_nearby_objects(session_id, x, y, z, map_id, radius).await;
        }
        Err(format!("No world client found for session {}", session_id).into())
    }

    pub async fn receive_world_event(&self, session_id: &str) -> Option<world::WorldEvent> {
        let mut receivers = self.event_receivers.lock().await;
        if let Some(receiver) = receivers.get_mut(session_id) {
            receiver.recv().await
        } else {
            None
        }
    }
}
